"""
TikTok Automation Engine
Handles follow/unfollow automation with human-like behavior
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from loguru import logger
from dataclasses import dataclass

from camoufox_integration.browser_manager import BrowserManager
from models.browser_profile import BrowserProfile
from models.tiktok_account import TikTokAccount
from core.database import get_async_session


@dataclass
class AutomationConfig:
    """Configuration for automation tasks"""
    max_follows_per_day: int = 100
    max_unfollows_per_day: int = 50
    min_delay_between_actions: int = 30  # seconds
    max_delay_between_actions: int = 120  # seconds
    human_like_delays: bool = True
    respect_rate_limits: bool = True
    max_concurrent_profiles: int = 3
    session_duration_minutes: int = 60
    break_duration_minutes: int = 15


@dataclass
class AutomationStats:
    """Statistics for automation session"""
    follows_completed: int = 0
    unfollows_completed: int = 0
    errors_encountered: int = 0
    session_start_time: datetime = None
    last_action_time: datetime = None
    
    def __post_init__(self):
        if self.session_start_time is None:
            self.session_start_time = datetime.now()


class TikTokAutomationEngine:
    """Main automation engine for TikTok interactions"""
    
    def __init__(self, config: AutomationConfig = None):
        self.config = config or AutomationConfig()
        self.browser_manager = BrowserManager()
        self.active_sessions = {}
        self.stats = {}
        self.is_running = False
        
    async def start_automation(
        self,
        profile_id: int,
        target_usernames: List[str],
        action_type: str = "follow",
        custom_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Start automation for a specific profile"""
        
        try:
            # Get profile and account
            async for session in get_async_session():
                profile = await session.get(BrowserProfile, profile_id)
                if not profile:
                    return {"success": False, "error": "Profile not found"}
                
                # Get associated TikTok account
                account = None
                if profile.tiktok_accounts:
                    account = profile.tiktok_accounts[0]
                
                if not account:
                    return {"success": False, "error": "No TikTok account associated with profile"}
            
            # Apply custom configuration
            if custom_config:
                for key, value in custom_config.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
            
            # Initialize session
            session_id = f"{profile_id}_{int(time.time())}"
            self.stats[session_id] = AutomationStats()
            
            # Start automation task
            task = asyncio.create_task(
                self._run_automation_session(
                    session_id, profile, account, target_usernames, action_type
                )
            )
            
            self.active_sessions[session_id] = {
                "task": task,
                "profile_id": profile_id,
                "account": account,
                "start_time": datetime.now(),
                "status": "running"
            }
            
            logger.info(f"Started automation session {session_id} for profile {profile_id}")
            
            return {
                "success": True,
                "session_id": session_id,
                "message": f"Automation started for {len(target_usernames)} targets"
            }
            
        except Exception as e:
            logger.error(f"Failed to start automation: {e}")
            return {"success": False, "error": str(e)}
    
    async def _run_automation_session(
        self,
        session_id: str,
        profile: BrowserProfile,
        account: TikTokAccount,
        target_usernames: List[str],
        action_type: str
    ):
        """Run the main automation session"""
        
        browser = None
        context = None
        
        try:
            # Launch browser with profile
            browser = await self.browser_manager.create_browser_instance(
                profile=profile,
                headless=False  # Visible for better human-like behavior
            )
            
            context = await self.browser_manager.create_browser_context(
                browser, profile, None
            )
            
            page = await context.new_page()
            
            # Navigate to TikTok
            await page.goto("https://www.tiktok.com/", timeout=30000)
            await self._wait_human_like()
            
            # Check if logged in
            if not await self._is_logged_in(page):
                logger.warning(f"Session {session_id}: Not logged in, attempting login")
                login_success = await self._attempt_login(page, account)
                if not login_success:
                    raise Exception("Failed to login to TikTok")
            
            # Process targets
            for username in target_usernames:
                if not self._should_continue_session(session_id):
                    break
                
                try:
                    if action_type == "follow":
                        success = await self._follow_user(page, username, session_id)
                    elif action_type == "unfollow":
                        success = await self._unfollow_user(page, username, session_id)
                    else:
                        logger.error(f"Unknown action type: {action_type}")
                        continue
                    
                    if success:
                        if action_type == "follow":
                            self.stats[session_id].follows_completed += 1
                        else:
                            self.stats[session_id].unfollows_completed += 1
                    else:
                        self.stats[session_id].errors_encountered += 1
                    
                    # Human-like delay between actions
                    await self._wait_human_like()
                    
                    # Check if we need a break
                    if await self._should_take_break(session_id):
                        await self._take_break(session_id)
                    
                except Exception as e:
                    logger.error(f"Error processing {username}: {e}")
                    self.stats[session_id].errors_encountered += 1
                    await self._wait_human_like()
            
            # Update session status
            self.active_sessions[session_id]["status"] = "completed"
            logger.info(f"Automation session {session_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Automation session {session_id} failed: {e}")
            self.active_sessions[session_id]["status"] = "failed"
            self.active_sessions[session_id]["error"] = str(e)
            
        finally:
            # Cleanup
            if context:
                await self.browser_manager.close_context(context)
            if browser:
                await self.browser_manager.close_browser(browser)
    
    async def _follow_user(self, page, username: str, session_id: str) -> bool:
        """Follow a specific user"""
        
        try:
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await page.goto(profile_url, timeout=30000)
            await self._wait_human_like()
            
            # Look for follow button
            follow_selectors = [
                '[data-e2e="follow-button"]',
                'button[data-e2e="follow-button"]',
                'button:has-text("Follow")',
                'button:has-text("Theo dõi")',
                '.follow-button'
            ]
            
            follow_button = None
            for selector in follow_selectors:
                try:
                    follow_button = await page.wait_for_selector(selector, timeout=5000)
                    if follow_button:
                        break
                except:
                    continue
            
            if not follow_button:
                logger.warning(f"Follow button not found for {username}")
                return False
            
            # Check if already following
            button_text = await follow_button.text_content()
            if "following" in button_text.lower() or "đang theo dõi" in button_text.lower():
                logger.info(f"Already following {username}")
                return True
            
            # Click follow button with human-like behavior
            await self._human_like_click(follow_button)
            await self._wait_human_like()
            
            # Verify follow action
            await page.wait_for_timeout(2000)
            updated_text = await follow_button.text_content()
            
            if "following" in updated_text.lower() or "đang theo dõi" in updated_text.lower():
                logger.info(f"Successfully followed {username}")
                self.stats[session_id].last_action_time = datetime.now()
                return True
            else:
                logger.warning(f"Follow action may have failed for {username}")
                return False
                
        except Exception as e:
            logger.error(f"Error following {username}: {e}")
            return False
    
    async def _unfollow_user(self, page, username: str, session_id: str) -> bool:
        """Unfollow a specific user"""
        
        try:
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await page.goto(profile_url, timeout=30000)
            await self._wait_human_like()
            
            # Look for following/unfollow button
            following_selectors = [
                '[data-e2e="follow-button"]',
                'button[data-e2e="follow-button"]',
                'button:has-text("Following")',
                'button:has-text("Đang theo dõi")',
                '.following-button'
            ]
            
            following_button = None
            for selector in following_selectors:
                try:
                    following_button = await page.wait_for_selector(selector, timeout=5000)
                    if following_button:
                        button_text = await following_button.text_content()
                        if "following" in button_text.lower() or "đang theo dõi" in button_text.lower():
                            break
                except:
                    continue
            
            if not following_button:
                logger.warning(f"Following button not found for {username} (may not be following)")
                return False
            
            # Click unfollow button
            await self._human_like_click(following_button)
            await self._wait_human_like()
            
            # Handle confirmation dialog if present
            try:
                confirm_button = await page.wait_for_selector(
                    'button:has-text("Unfollow"), button:has-text("Bỏ theo dõi")',
                    timeout=3000
                )
                if confirm_button:
                    await self._human_like_click(confirm_button)
                    await self._wait_human_like()
            except:
                pass  # No confirmation dialog
            
            # Verify unfollow action
            await page.wait_for_timeout(2000)
            
            # Check if button changed back to "Follow"
            try:
                follow_button = await page.wait_for_selector(
                    'button:has-text("Follow"), button:has-text("Theo dõi")',
                    timeout=3000
                )
                if follow_button:
                    logger.info(f"Successfully unfollowed {username}")
                    self.stats[session_id].last_action_time = datetime.now()
                    return True
            except:
                pass
            
            logger.warning(f"Unfollow action may have failed for {username}")
            return False
                
        except Exception as e:
            logger.error(f"Error unfollowing {username}: {e}")
            return False
    
    async def _is_logged_in(self, page) -> bool:
        """Check if user is logged in to TikTok"""
        
        try:
            # Look for login indicators
            login_indicators = [
                '[data-e2e="profile-icon"]',
                '.avatar',
                'button[data-e2e="top-profile-avatar"]'
            ]
            
            for selector in login_indicators:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking login status: {e}")
            return False
    
    async def _attempt_login(self, page, account: TikTokAccount) -> bool:
        """Attempt to login using saved cookies or manual intervention"""
        
        try:
            # Try to load saved cookies
            if account.cookies:
                await page.context.add_cookies(account.cookies)
                await page.reload()
                await self._wait_human_like()
                
                if await self._is_logged_in(page):
                    logger.info("Successfully logged in using saved cookies")
                    return True
            
            # If cookies don't work, require manual login
            logger.warning("Automatic login failed. Manual intervention required.")
            
            # Navigate to login page
            await page.goto("https://www.tiktok.com/login", timeout=30000)
            
            # Wait for manual login (up to 5 minutes)
            for _ in range(60):  # 60 * 5 seconds = 5 minutes
                await page.wait_for_timeout(5000)
                if await self._is_logged_in(page):
                    # Save new cookies
                    cookies = await page.context.cookies()
                    account.cookies = cookies
                    account.is_logged_in = True
                    
                    # Update in database
                    async for session in get_async_session():
                        session.add(account)
                        await session.commit()
                    
                    logger.info("Manual login successful, cookies saved")
                    return True
            
            logger.error("Manual login timeout")
            return False
            
        except Exception as e:
            logger.error(f"Login attempt failed: {e}")
            return False
    
    async def _human_like_click(self, element):
        """Perform human-like click with random delays and movements"""
        
        # Random delay before click
        await asyncio.sleep(random.uniform(0.1, 0.5))
        
        # Get element position
        box = await element.bounding_box()
        if box:
            # Random position within element
            x = box['x'] + random.uniform(0.2, 0.8) * box['width']
            y = box['y'] + random.uniform(0.2, 0.8) * box['height']
            
            # Move to position and click
            page = element.page
            await page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.05, 0.15))
            await page.mouse.click(x, y)
        else:
            # Fallback to regular click
            await element.click()
    
    async def _wait_human_like(self):
        """Wait with human-like random delays"""
        
        if self.config.human_like_delays:
            base_delay = random.uniform(
                self.config.min_delay_between_actions,
                self.config.max_delay_between_actions
            )
            
            # Add some randomness
            variation = random.uniform(0.8, 1.2)
            final_delay = base_delay * variation
            
            logger.debug(f"Waiting {final_delay:.1f} seconds")
            await asyncio.sleep(final_delay)
        else:
            await asyncio.sleep(self.config.min_delay_between_actions)
    
    def _should_continue_session(self, session_id: str) -> bool:
        """Check if session should continue based on limits and time"""
        
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        stats = self.stats[session_id]
        
        # Check if manually stopped
        if session["status"] != "running":
            return False
        
        # Check daily limits
        if stats.follows_completed >= self.config.max_follows_per_day:
            logger.info(f"Daily follow limit reached for session {session_id}")
            return False
        
        if stats.unfollows_completed >= self.config.max_unfollows_per_day:
            logger.info(f"Daily unfollow limit reached for session {session_id}")
            return False
        
        # Check session duration
        session_duration = datetime.now() - session["start_time"]
        if session_duration.total_seconds() > self.config.session_duration_minutes * 60:
            logger.info(f"Session duration limit reached for session {session_id}")
            return False
        
        return True
    
    async def _should_take_break(self, session_id: str) -> bool:
        """Determine if automation should take a break"""
        
        stats = self.stats[session_id]
        
        # Take break every 20-30 actions
        total_actions = stats.follows_completed + stats.unfollows_completed
        if total_actions > 0 and total_actions % random.randint(20, 30) == 0:
            return True
        
        # Take break if running for more than 45 minutes
        if stats.last_action_time:
            time_since_start = datetime.now() - stats.session_start_time
            if time_since_start.total_seconds() > 45 * 60:
                return True
        
        return False
    
    async def _take_break(self, session_id: str):
        """Take a human-like break"""
        
        break_duration = random.uniform(
            self.config.break_duration_minutes * 0.5,
            self.config.break_duration_minutes * 1.5
        ) * 60  # Convert to seconds
        
        logger.info(f"Taking break for {break_duration/60:.1f} minutes")
        await asyncio.sleep(break_duration)
    
    async def stop_automation(self, session_id: str) -> Dict[str, Any]:
        """Stop a specific automation session"""
        
        if session_id not in self.active_sessions:
            return {"success": False, "error": "Session not found"}
        
        try:
            session = self.active_sessions[session_id]
            session["status"] = "stopping"
            
            # Cancel the task
            if "task" in session and not session["task"].done():
                session["task"].cancel()
            
            session["status"] = "stopped"
            
            logger.info(f"Automation session {session_id} stopped")
            
            return {
                "success": True,
                "message": "Automation stopped",
                "stats": self.stats.get(session_id, {})
            }
            
        except Exception as e:
            logger.error(f"Error stopping session {session_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get statistics for a specific session"""
        
        if session_id not in self.stats:
            return {"error": "Session not found"}
        
        stats = self.stats[session_id]
        session = self.active_sessions.get(session_id, {})
        
        return {
            "session_id": session_id,
            "status": session.get("status", "unknown"),
            "follows_completed": stats.follows_completed,
            "unfollows_completed": stats.unfollows_completed,
            "errors_encountered": stats.errors_encountered,
            "session_start_time": stats.session_start_time.isoformat(),
            "last_action_time": stats.last_action_time.isoformat() if stats.last_action_time else None,
            "total_actions": stats.follows_completed + stats.unfollows_completed
        }
    
    def get_all_sessions(self) -> List[Dict[str, Any]]:
        """Get information about all active sessions"""
        
        sessions = []
        for session_id in self.active_sessions:
            sessions.append(self.get_session_stats(session_id))
        
        return sessions
