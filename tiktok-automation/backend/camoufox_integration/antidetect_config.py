"""
Antidetect Configuration for enhanced stealth capabilities
"""

import random
import json
from typing import Dict, Any, List, Optional
from loguru import logger


class AntidetectConfig:
    """Advanced antidetect configuration generator"""
    
    def __init__(self):
        self.stealth_patterns = self._load_stealth_patterns()
        self.human_behavior_config = self._load_human_behavior_config()
    
    def get_enhanced_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance base configuration with antidetect features"""
        
        enhanced_config = {}
        
        # Add human-like cursor movement
        enhanced_config.update(self._get_cursor_config())
        
        # Add timing randomization
        enhanced_config.update(self._get_timing_config())
        
        # Add behavioral patterns
        enhanced_config.update(self._get_behavioral_config())
        
        # Add stealth enhancements
        enhanced_config.update(self._get_stealth_config(base_config))
        
        # Add font randomization
        enhanced_config.update(self._get_font_config())
        
        # Add WebRTC configuration
        enhanced_config.update(self._get_webrtc_config())
        
        logger.debug("Enhanced antidetect configuration generated")
        return enhanced_config
    
    def _get_cursor_config(self) -> Dict[str, Any]:
        """Configure human-like cursor movement"""
        
        return {
            "humanize": True,
            "humanize:maxTime": random.uniform(1.0, 2.5),
            "showcursor": False,  # Don't show cursor highlighter in production
        }
    
    def _get_timing_config(self) -> Dict[str, Any]:
        """Configure timing randomization"""
        
        # Add slight randomization to timing-sensitive properties
        config = {}
        
        # Randomize performance timing
        if random.choice([True, False]):
            config["performance.timing.randomize"] = True
        
        return config
    
    def _get_behavioral_config(self) -> Dict[str, Any]:
        """Configure human behavioral patterns"""
        
        config = {}
        
        # Mouse movement patterns
        config.update({
            "mouse.movement.natural": True,
            "mouse.movement.variance": random.uniform(0.1, 0.3),
            "mouse.click.delay": random.randint(50, 200),  # ms
        })
        
        # Keyboard patterns
        config.update({
            "keyboard.typing.natural": True,
            "keyboard.typing.speed": random.randint(80, 120),  # WPM
            "keyboard.typing.variance": random.uniform(0.1, 0.25),
        })
        
        # Scroll patterns
        config.update({
            "scroll.behavior.smooth": True,
            "scroll.speed.variance": random.uniform(0.8, 1.2),
        })
        
        return config
    
    def _get_stealth_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Configure stealth enhancements"""
        
        config = {}
        
        # WebDriver detection evasion
        config.update({
            "webdriver.detection.evasion": True,
            "automation.detection.evasion": True,
        })
        
        # Canvas fingerprinting protection
        config.update({
            "canvas.fingerprinting.protection": True,
            "canvas.noise.enabled": True,
            "canvas.noise.amount": random.uniform(0.001, 0.01),
        })
        
        # Audio fingerprinting protection
        config.update({
            "audio.fingerprinting.protection": True,
            "audio.context.noise": True,
        })
        
        # Font fingerprinting protection
        config.update({
            "font.fingerprinting.protection": True,
            "font.metrics.randomization": True,
        })
        
        # WebGL fingerprinting protection
        if not any(key.startswith("webGl:") for key in base_config.keys()):
            config.update({
                "webgl.fingerprinting.protection": True,
                "webgl.noise.enabled": True,
            })
        
        return config
    
    def _get_font_config(self) -> Dict[str, Any]:
        """Configure font randomization"""
        
        # Select random fonts from common system fonts
        common_fonts = [
            "Arial", "Helvetica", "Times New Roman", "Courier New",
            "Verdana", "Georgia", "Palatino", "Garamond",
            "Bookman", "Comic Sans MS", "Trebuchet MS", "Arial Black",
            "Impact", "Lucida Sans Unicode", "Tahoma", "Lucida Console"
        ]
        
        # Randomize font list
        selected_fonts = random.sample(common_fonts, random.randint(8, 12))
        
        return {
            "fonts": selected_fonts,
            "font.randomization.enabled": True,
        }
    
    def _get_webrtc_config(self) -> Dict[str, Any]:
        """Configure WebRTC for IP protection"""
        
        # Generate random local IP
        local_ip = f"192.168.{random.randint(1, 254)}.{random.randint(1, 254)}"
        
        return {
            "webrtc:ipv4": local_ip,
            "webrtc.leak.protection": True,
        }
    
    def get_human_delay(self, min_delay: float = 1.0, max_delay: float = 3.0) -> float:
        """Generate human-like delay with natural distribution"""
        
        # Use normal distribution for more natural timing
        mean = (min_delay + max_delay) / 2
        std_dev = (max_delay - min_delay) / 6  # 99.7% within range
        
        delay = random.normalvariate(mean, std_dev)
        
        # Ensure within bounds
        delay = max(min_delay, min(max_delay, delay))
        
        return delay
    
    def get_typing_pattern(self, text: str) -> List[Dict[str, Any]]:
        """Generate human-like typing pattern for text input"""
        
        pattern = []
        base_delay = random.uniform(80, 120)  # Base typing speed (ms per char)
        
        for i, char in enumerate(text):
            # Calculate delay for this character
            char_delay = base_delay
            
            # Add variance based on character type
            if char.isupper():
                char_delay *= random.uniform(1.2, 1.5)  # Slower for capitals
            elif char in ".,!?;:":
                char_delay *= random.uniform(1.1, 1.3)  # Slower for punctuation
            elif char == " ":
                char_delay *= random.uniform(1.5, 2.0)  # Slower for spaces
            
            # Add random variance
            char_delay *= random.uniform(0.7, 1.3)
            
            # Occasional longer pauses (thinking)
            if random.random() < 0.05:  # 5% chance
                char_delay *= random.uniform(2.0, 4.0)
            
            pattern.append({
                "char": char,
                "delay": char_delay,
                "timestamp": sum(p["delay"] for p in pattern) + char_delay
            })
        
        return pattern
    
    def get_scroll_pattern(self, distance: int) -> List[Dict[str, Any]]:
        """Generate human-like scroll pattern"""
        
        pattern = []
        remaining = abs(distance)
        direction = 1 if distance > 0 else -1
        
        while remaining > 0:
            # Random scroll amount
            scroll_amount = min(remaining, random.randint(50, 200))
            
            # Random delay between scrolls
            delay = random.uniform(100, 300)
            
            pattern.append({
                "amount": scroll_amount * direction,
                "delay": delay,
                "easing": random.choice(["linear", "ease-out", "ease-in-out"])
            })
            
            remaining -= scroll_amount
        
        return pattern
    
    def _load_stealth_patterns(self) -> Dict[str, Any]:
        """Load stealth behavior patterns"""
        
        return {
            "mouse_movements": {
                "natural_curve": True,
                "speed_variance": 0.2,
                "pause_probability": 0.1,
                "overshoot_probability": 0.05,
            },
            "click_patterns": {
                "double_click_threshold": random.randint(300, 500),
                "click_duration": random.randint(50, 150),
                "release_variance": random.randint(10, 50),
            },
            "page_interaction": {
                "scroll_before_click": 0.3,  # 30% chance
                "hover_before_click": 0.4,   # 40% chance
                "random_movements": 0.2,     # 20% chance
            }
        }
    
    def _load_human_behavior_config(self) -> Dict[str, Any]:
        """Load human behavior configuration"""
        
        return {
            "reading_speed": {
                "words_per_minute": random.randint(200, 300),
                "variance": 0.3,
            },
            "attention_span": {
                "focus_duration": random.randint(30, 120),  # seconds
                "break_probability": 0.1,
            },
            "interaction_patterns": {
                "exploration_probability": 0.15,
                "back_button_usage": 0.1,
                "new_tab_probability": 0.05,
            }
        }
    
    def should_take_break(self, session_duration: int) -> bool:
        """Determine if a human-like break should be taken"""
        
        # Probability increases with session duration
        base_probability = 0.01  # 1% per minute
        duration_factor = session_duration / 60  # Convert to minutes
        
        probability = min(0.3, base_probability * duration_factor)
        
        return random.random() < probability
    
    def get_break_duration(self) -> float:
        """Get human-like break duration"""
        
        # Most breaks are short, some are longer
        if random.random() < 0.8:  # 80% short breaks
            return random.uniform(5, 30)  # 5-30 seconds
        else:  # 20% longer breaks
            return random.uniform(60, 300)  # 1-5 minutes
