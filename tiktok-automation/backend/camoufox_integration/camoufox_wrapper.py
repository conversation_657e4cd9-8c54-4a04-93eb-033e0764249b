"""
Local Camoufox Management System
Handles local installation, caching, and version management of Camoufox
"""

import asyncio
import os
import json
import hashlib
import shutil
import platform
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger
# import aiohttp  # Not needed for current implementation
# import aiofiles  # Will use regular file operations for now

try:
    from camoufox.async_api import Async<PERSON>amoufox
    from camoufox import pkgman
    CAMOUFOX_AVAILABLE = True
except ImportError:
    CAMOUFOX_AVAILABLE = False
    logger.error("Camoufox not available - please install: pip install camoufox")


class CamoufoxBinaryManager:
    """Manages local Camoufox binary installation and versioning"""

    def __init__(self, app_data_dir: Optional[str] = None):
        # Setup directories
        if app_data_dir:
            self.app_data_dir = Path(app_data_dir)
        else:
            # Default app data directory
            if platform.system() == "Windows":
                self.app_data_dir = Path.home() / "AppData" / "Local" / "TikTokAutomation"
            elif platform.system() == "Darwin":  # macOS
                self.app_data_dir = Path.home() / "Library" / "Application Support" / "TikTokAutomation"
            else:  # Linux
                self.app_data_dir = Path.home() / ".local" / "share" / "TikTokAutomation"

        self.camoufox_dir = self.app_data_dir / "camoufox"
        self.version_file = self.camoufox_dir / "version.json"
        self.binary_dir = self.camoufox_dir / "binary"

        # Create directories
        self.camoufox_dir.mkdir(parents=True, exist_ok=True)
        self.binary_dir.mkdir(parents=True, exist_ok=True)

        # Version info
        self.current_version = None
        self.latest_version = None

        logger.info(f"Camoufox manager initialized: {self.camoufox_dir}")

    async def ensure_camoufox_available(self) -> bool:
        """Ensure Camoufox is available locally, download if needed"""

        if not CAMOUFOX_AVAILABLE:
            logger.error("Camoufox package not installed")
            return False

        # Check if we have a local version
        if await self._is_local_version_available():
            logger.info("Local Camoufox version available")
            return True

        # Download Camoufox binary
        logger.info("Local Camoufox not found, downloading...")
        return await self._download_camoufox()

    async def _is_local_version_available(self) -> bool:
        """Check if local Camoufox version is available and valid"""

        if not self.version_file.exists():
            return False

        try:
            with open(self.version_file, 'r') as f:
                version_data = json.loads(f.read())
                self.current_version = version_data.get('version')

            # Check if binary exists
            if self.current_version and (self.binary_dir / self.current_version).exists():
                logger.info(f"Found local Camoufox version: {self.current_version}")
                return True

        except Exception as e:
            logger.warning(f"Error reading version file: {e}")

        return False

    async def _download_camoufox(self) -> bool:
        """Download Camoufox binary and cache locally"""

        try:
            # Use camoufox's built-in download mechanism
            logger.info("Downloading Camoufox binary...")

            # Use camoufox's built-in installation
            # For now, assume Camoufox is already installed via pip
            # The binary will be available through the package
            download_path = self.binary_dir / "latest"
            download_path.mkdir(exist_ok=True)

            # Create a marker file to indicate "download" completed
            marker_file = download_path / "installed.marker"
            marker_file.touch()

            # Save version info
            version_info = {
                "version": "latest",
                "download_date": str(asyncio.get_event_loop().time()),
                "path": str(download_path)
            }

            with open(self.version_file, 'w') as f:
                f.write(json.dumps(version_info, indent=2))

            self.current_version = "latest"
            logger.info("Camoufox downloaded successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to download Camoufox: {e}")
            return False

    def get_camoufox_path(self) -> Optional[str]:
        """Get path to local Camoufox installation"""

        if self.current_version:
            camoufox_path = self.binary_dir / self.current_version
            if camoufox_path.exists():
                return str(camoufox_path)

        return None


class LocalCamoufoxWrapper:
    """Optimized Camoufox wrapper using local binaries only"""

    def __init__(self, app_data_dir: Optional[str] = None):
        self.binary_manager = CamoufoxBinaryManager(app_data_dir)
        self._initialized = False

    async def initialize(self) -> bool:
        """Initialize and ensure Camoufox is available"""

        if self._initialized:
            return True

        if not CAMOUFOX_AVAILABLE:
            logger.error("Camoufox package not installed")
            return False

        # Ensure Camoufox binary is available
        success = await self.binary_manager.ensure_camoufox_available()
        if success:
            self._initialized = True
            logger.info("Local Camoufox wrapper initialized successfully")

        return success

    async def create_browser(
        self,
        config: Optional[Dict[str, Any]] = None,
        proxy: Optional[Dict[str, Any]] = None,
        headless: bool = True,
        user_data_dir: Optional[str] = None,
        timeout: int = 30000,
        **kwargs
    ):
        """Create Camoufox browser instance using local binary"""

        # Ensure initialized
        if not self._initialized:
            if not await self.initialize():
                raise Exception("Failed to initialize Camoufox")

        try:
            # Prepare optimized config
            optimized_config = self._prepare_optimized_config(config)

            # Let Camoufox handle the executable path automatically
            # Don't set executable_path to let Camoufox use its built-in Firefox
            executable_path = None
            logger.info("Using Camoufox's built-in Firefox binary")

            # Debug: Log the final config before sending to Camoufox
            logger.info(f"Final config keys before Camoufox: {list(optimized_config.keys())}")
            if "navigator.deviceMemory" in optimized_config:
                logger.warning("Found navigator.deviceMemory in final config - removing it")
                del optimized_config["navigator.deviceMemory"]

            # Fix navigator.doNotTrack if it's None
            if optimized_config.get("navigator.doNotTrack") is None:
                logger.warning("Found navigator.doNotTrack = None, fixing to '0'")
                optimized_config["navigator.doNotTrack"] = "0"

            # Remove unsupported properties that are not in Camoufox properties.json
            unsupported_properties = [
                "mouse.movement.natural", "mouse.movement.variance", "mouse.click.delay",
                "keyboard.typing.natural", "keyboard.typing.speed", "keyboard.typing.variance",
                "scroll.behavior.smooth", "scroll.speed.variance",
                "webdriver.detection.evasion", "automation.detection.evasion",
                "canvas.fingerprinting.protection", "canvas.noise.enabled", "canvas.noise.amount",
                "audio.fingerprinting.protection", "audio.context.noise",
                "font.fingerprinting.protection", "font.metrics.randomization", "font.randomization.enabled",
                "webgl.fingerprinting.protection", "webgl.noise.enabled",
                "webrtc.leak.protection", "performance.timing.randomize",
                "firefox_version",  # Internal property, not for Camoufox
                "is_mobile",  # Internal property, not for Camoufox
                "timezone"  # Internal property, not for Camoufox
            ]

            for prop in unsupported_properties:
                if prop in optimized_config:
                    logger.debug(f"Removing unsupported property: {prop}")
                    del optimized_config[prop]

            # Try to create Camoufox instance first
            try:
                from camoufox.async_api import AsyncCamoufox

                # Create Camoufox instance with proper configuration
                camoufox = AsyncCamoufox(
                    config=optimized_config,
                    proxy=proxy,
                    headless=headless,
                    timeout=timeout,
                    addons=[],  # Disable addons to avoid downloads
                    humanize=True,  # Enable humanized cursor movement
                    geoip=False,  # Disable geoip to avoid issues
                    block_webrtc=False,  # Don't block WebRTC completely
                    i_know_what_im_doing=True,  # Suppress warnings
                    **kwargs
                )

                browser = await camoufox.__aenter__()
                logger.info("Camoufox browser created successfully with Firefox")
                return browser

            except Exception as e:
                logger.warning(f"Failed to create Camoufox browser: {e}")
                # Fall through to fallback

            # Fallback: Use regular Playwright Firefox when Camoufox is not available
            logger.warning("Camoufox binary not available, falling back to regular Firefox")

            from playwright.async_api import async_playwright

            playwright = await async_playwright().__aenter__()

            # Launch regular Firefox with anti-detection args
            browser = await playwright.firefox.launch(
                headless=headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )

            # Store fingerprint config for later use in browser_manager
            browser._fingerprint_config = optimized_config

            logger.info("Regular Firefox browser created")
            return browser

        except Exception as e:
            logger.error(f"Failed to create Camoufox browser: {e}")
            raise Exception(f"Camoufox browser creation failed: {e}")

    def _prepare_optimized_config(self, config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Prepare optimized config for performance"""

        if config:
            return config

        # Default optimized config for performance
        return {
            "os": "windows",
            "screen": {
                "width": 1920,
                "height": 1080,
                "availWidth": 1920,
                "availHeight": 1040
            },
            "locale": "en-US",
            "timezone": "America/New_York",
            # Minimal config to avoid unnecessary processing
            "webgl": {"vendor": "Google Inc.", "renderer": "ANGLE"},
            "fonts": ["Arial", "Times New Roman", "Courier New"]
        }


# Global instance - initialize once and reuse
local_camoufox = LocalCamoufoxWrapper()
