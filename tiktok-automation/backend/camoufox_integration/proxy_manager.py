"""
Proxy Manager for handling different proxy types and validation
"""

import asyncio
import aiohttp
import socket
import time
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger

from models.proxy import Proxy, ProxyType, ProxyStatus
from core.config import settings


class ProxyManager:
    """Manages proxy connections and validation"""
    
    def __init__(self):
        self.active_proxies: Dict[int, Proxy] = {}
        self.proxy_pool: List[Proxy] = []
        self.current_proxy_index = 0
        
    async def get_proxy_config(self, proxy: Proxy) -> Optional[Dict[str, Any]]:
        """Get proxy configuration for Camoufox"""
        
        if not proxy or not proxy.is_active:
            return None
        
        try:
            # Validate proxy before use
            is_valid = await self.validate_proxy(proxy)
            if not is_valid:
                logger.warning(f"Proxy {proxy.name} failed validation")
                return None
            
            # Build proxy configuration for Camoufox (Playwright format)
            # Note: Playwright/Camoufox doesn't support SOCKS5 with authentication
            # For SOCKS5 with auth, we'll use HTTP format as workaround
            if proxy.proxy_type == ProxyType.HTTP:
                server = f"http://{proxy.host}:{proxy.port}"
            elif proxy.proxy_type == ProxyType.HTTPS:
                server = f"https://{proxy.host}:{proxy.port}"
            elif proxy.proxy_type in [ProxyType.SOCKS4, ProxyType.SOCKS5]:
                # For SOCKS with authentication, use HTTP format as workaround
                if proxy.username and proxy.password:
                    server = f"http://{proxy.host}:{proxy.port}"
                else:
                    server = f"socks5://{proxy.host}:{proxy.port}"
            else:
                # Default to http for unknown types
                server = f"http://{proxy.host}:{proxy.port}"

            config = {
                "server": server
            }

            # Add authentication if available
            if proxy.username:
                config["username"] = proxy.username

            if proxy.password:
                config["password"] = proxy.password
            
            # SSH specific configuration
            if proxy.proxy_type == ProxyType.SSH:
                config.update(await self._get_ssh_config(proxy))
            
            logger.info(f"Proxy config prepared for {proxy.name}")
            return config
            
        except Exception as e:
            logger.error(f"Error preparing proxy config for {proxy.name}: {e}")
            return None
    
    async def validate_proxy(self, proxy: Proxy, timeout: int = None) -> bool:
        """Validate proxy connectivity and performance"""
        
        timeout = timeout or settings.PROXY_TIMEOUT
        start_time = time.time()
        
        try:
            # Test different validation methods based on proxy type
            if proxy.proxy_type in [ProxyType.HTTP, ProxyType.HTTPS]:
                success = await self._validate_http_proxy(proxy, timeout)
            elif proxy.proxy_type in [ProxyType.SOCKS4, ProxyType.SOCKS5]:
                success = await self._validate_socks_proxy(proxy, timeout)
            elif proxy.proxy_type == ProxyType.SSH:
                success = await self._validate_ssh_proxy(proxy, timeout)
            else:
                logger.warning(f"Unknown proxy type: {proxy.proxy_type}")
                return False
            
            # Calculate response time
            response_time = int((time.time() - start_time) * 1000)
            
            # Update proxy statistics
            proxy.update_stats(success, response_time)
            proxy.update_health_check(success)
            
            if success:
                logger.info(f"Proxy {proxy.name} validated successfully ({response_time}ms)")
            else:
                logger.warning(f"Proxy {proxy.name} validation failed")
            
            return success
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Proxy validation error for {proxy.name}: {error_msg}")
            
            proxy.update_stats(False)
            proxy.update_health_check(False, error_msg)
            
            return False
    
    async def _validate_http_proxy(self, proxy: Proxy, timeout: int) -> bool:
        """Validate HTTP/HTTPS proxy"""

        try:
            # Build proxy URL
            proxy_url = proxy.proxy_url

            # Test with multiple endpoints for better reliability
            test_urls = [
                "http://httpbin.org/ip",
                "http://icanhazip.com",
                "http://ipinfo.io/ip"
            ]

            # Test with a simple HTTP request using proxy
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as session:

                for test_url in test_urls:
                    try:
                        async with session.get(test_url, proxy=proxy_url) as response:
                            if response.status == 200:
                                # Try to get IP address from response
                                try:
                                    if "httpbin.org" in test_url:
                                        data = await response.json()
                                        if "origin" in data:
                                            proxy.ip_address = data["origin"].split(",")[0].strip()
                                    else:
                                        ip_text = await response.text()
                                        proxy.ip_address = ip_text.strip()
                                except:
                                    pass  # IP detection failed, but proxy works

                                return True
                    except Exception as e:
                        logger.debug(f"Failed to test {test_url} via HTTP proxy: {e}")
                        continue

                return False

        except Exception as e:
            logger.debug(f"HTTP proxy validation failed: {e}")
            return False
    
    async def _validate_socks_proxy(self, proxy: Proxy, timeout: int) -> bool:
        """Validate SOCKS4/SOCKS5 proxy"""

        try:
            # Import socks from pysocks package
            import socks
            import socket
            import asyncio

            # Run SOCKS validation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._test_socks_connection,
                proxy,
                timeout
            )

            return result

        except Exception as e:
            logger.debug(f"SOCKS proxy validation failed: {e}")
            return False

    def _test_socks_connection(self, proxy: Proxy, timeout: int) -> bool:
        """Test SOCKS connection in synchronous context"""

        try:
            import socks
            import socket
            import requests

            # Test SOCKS proxy by making HTTP request through it
            proxies = {
                'http': proxy.proxy_url,
                'https': proxy.proxy_url
            }

            # Test URLs for IP detection
            test_urls = [
                "http://httpbin.org/ip",
                "http://icanhazip.com",
                "http://ipinfo.io/ip"
            ]

            for test_url in test_urls:
                try:
                    response = requests.get(
                        test_url,
                        proxies=proxies,
                        timeout=timeout,
                        verify=False
                    )

                    if response.status_code == 200:
                        # Try to extract IP address
                        try:
                            if "httpbin.org" in test_url:
                                data = response.json()
                                if "origin" in data:
                                    proxy.ip_address = data["origin"].split(",")[0].strip()
                            else:
                                ip_text = response.text.strip()
                                # Validate IP format
                                import re
                                ip_pattern = r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'
                                if re.match(ip_pattern, ip_text):
                                    proxy.ip_address = ip_text
                        except Exception as e:
                            logger.debug(f"Failed to extract IP from {test_url}: {e}")

                        return True

                except Exception as e:
                    logger.debug(f"Failed to test {test_url} via SOCKS proxy: {e}")
                    continue

            return False

        except Exception as e:
            logger.debug(f"SOCKS connection test failed: {e}")
            return False
    
    async def _validate_ssh_proxy(self, proxy: Proxy, timeout: int) -> bool:
        """Validate SSH proxy/tunnel"""
        
        try:
            import paramiko
            
            # Create SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect with timeout
            ssh.connect(
                hostname=proxy.host,
                port=proxy.port,
                username=proxy.username,
                password=proxy.password,
                pkey=self._load_ssh_key(proxy.ssh_private_key, proxy.ssh_passphrase),
                timeout=timeout
            )
            
            # Test command
            stdin, stdout, stderr = ssh.exec_command("echo 'test'")
            result = stdout.read().decode().strip()
            
            ssh.close()
            
            return result == "test"
            
        except Exception as e:
            logger.debug(f"SSH proxy validation failed: {e}")
            return False
    
    async def get_proxy_geolocation(self, proxy: Proxy) -> Optional[Dict[str, str]]:
        """Get geolocation information for proxy IP"""
        
        try:
            if not proxy.ip_address:
                # Try to detect IP first
                await self.validate_proxy(proxy)
            
            if not proxy.ip_address:
                return None
            
            # Use a geolocation service
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"http://ip-api.com/json/{proxy.ip_address}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get("status") == "success":
                            geo_info = {
                                "country": data.get("country"),
                                "city": data.get("city"),
                                "region": data.get("regionName"),
                                "timezone": data.get("timezone"),
                                "lat": data.get("lat"),
                                "lon": data.get("lon")
                            }
                            
                            # Update proxy with geo info
                            proxy.country = geo_info["country"]
                            proxy.city = geo_info["city"]
                            
                            return geo_info
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting proxy geolocation: {e}")
            return None
    
    async def rotate_proxy(self, proxy_list: List[Proxy]) -> Optional[Proxy]:
        """Rotate to next available proxy"""
        
        if not proxy_list:
            return None
        
        # Filter active proxies
        active_proxies = [p for p in proxy_list if p.is_active and p.status == ProxyStatus.ACTIVE]
        
        if not active_proxies:
            logger.warning("No active proxies available for rotation")
            return None
        
        # Simple round-robin rotation
        self.current_proxy_index = (self.current_proxy_index + 1) % len(active_proxies)
        selected_proxy = active_proxies[self.current_proxy_index]
        
        logger.info(f"Rotated to proxy: {selected_proxy.name}")
        return selected_proxy
    
    async def health_check_all(self, proxy_list: List[Proxy]) -> Dict[str, Any]:
        """Perform health check on all proxies"""
        
        logger.info(f"Starting health check for {len(proxy_list)} proxies")
        
        results = {
            "total": len(proxy_list),
            "active": 0,
            "inactive": 0,
            "errors": 0,
            "details": []
        }
        
        # Check proxies concurrently (but limit concurrency)
        semaphore = asyncio.Semaphore(5)  # Max 5 concurrent checks
        
        async def check_proxy(proxy):
            async with semaphore:
                success = await self.validate_proxy(proxy)
                
                result = {
                    "id": proxy.id,
                    "name": proxy.name,
                    "success": success,
                    "response_time": proxy.response_time_ms,
                    "error": proxy.last_error
                }
                
                if success:
                    results["active"] += 1
                elif proxy.status == ProxyStatus.ERROR:
                    results["errors"] += 1
                else:
                    results["inactive"] += 1
                
                results["details"].append(result)
        
        # Run health checks
        await asyncio.gather(*[check_proxy(proxy) for proxy in proxy_list])
        
        logger.info(f"Health check completed: {results['active']} active, {results['inactive']} inactive, {results['errors']} errors")
        
        return results
    
    def _load_ssh_key(self, private_key_data: Optional[str], passphrase: Optional[str]):
        """Load SSH private key"""
        
        if not private_key_data:
            return None
        
        try:
            import paramiko
            from io import StringIO
            
            key_file = StringIO(private_key_data)
            
            # Try different key types
            for key_class in [paramiko.RSAKey, paramiko.DSSKey, paramiko.ECDSAKey, paramiko.Ed25519Key]:
                try:
                    key_file.seek(0)
                    return key_class.from_private_key(key_file, password=passphrase)
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading SSH key: {e}")
            return None
    
    async def _get_ssh_config(self, proxy: Proxy) -> Dict[str, Any]:
        """Get SSH-specific configuration"""
        
        config = {}
        
        if proxy.ssh_private_key:
            config["ssh_private_key"] = proxy.ssh_private_key
        
        if proxy.ssh_passphrase:
            config["ssh_passphrase"] = proxy.ssh_passphrase
        
        return config
