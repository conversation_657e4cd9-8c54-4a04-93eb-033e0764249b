"""
Browser Profile model for antidetect browser configurations
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from core.database import Base


class BrowserProfile(Base):
    """Browser profile with antidetect configurations"""
    
    __tablename__ = "browser_profiles"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic info
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Proxy configuration
    proxy_id = Column(Integer, ForeignKey("proxies.id"), nullable=True, index=True)
    
    # Browser fingerprint configuration (stored as JSON)
    fingerprint_config = Column(JSON, nullable=False, default=dict)
    
    # User agent and navigator properties
    user_agent = Column(Text, nullable=True)
    navigator_config = Column(JSON, nullable=True, default=dict)
    
    # Screen and window configuration
    screen_config = Column(JSON, nullable=True, default=dict)
    window_config = Column(JSON, nullable=True, default=dict)
    
    # WebGL configuration
    webgl_config = Column(JSON, nullable=True, default=dict)
    
    # Audio context configuration
    audio_config = Column(JSON, nullable=True, default=dict)
    
    # Geolocation and timezone
    geolocation_config = Column(JSON, nullable=True, default=dict)
    timezone = Column(String(100), nullable=True)
    locale = Column(String(20), nullable=True)
    
    # Font configuration
    fonts_config = Column(JSON, nullable=True, default=dict)
    
    # Additional browser settings
    browser_settings = Column(JSON, nullable=True, default=dict)
    
    # Usage statistics
    usage_count = Column(Integer, default=0, nullable=False)
    last_used = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    proxy = relationship("Proxy", back_populates="browser_profiles")
    tiktok_accounts = relationship("TikTokAccount", back_populates="browser_profile")
    follow_tasks = relationship("FollowTask", back_populates="browser_profile")
    automation_tasks = relationship("AutomationTask", back_populates="profile", cascade="all, delete-orphan")
    
    @hybrid_property
    def proxy_info(self) -> Optional[Dict[str, Any]]:
        """Get proxy information if available"""
        if hasattr(self, '_proxy') and self._proxy:
            return {
                "type": self._proxy.proxy_type,
                "host": self._proxy.host,
                "port": self._proxy.port,
                "username": self._proxy.username,
                "is_active": self._proxy.is_active
            }
        return None
    
    def get_camoufox_config(self) -> Dict[str, Any]:
        """Generate Camoufox configuration from profile settings"""
        config = {}
        
        # Navigator configuration
        if self.navigator_config:
            config.update(self.navigator_config)
        
        # User agent
        if self.user_agent:
            config["navigator.userAgent"] = self.user_agent
        
        # Screen configuration
        if self.screen_config:
            for key, value in self.screen_config.items():
                config[f"screen.{key}"] = value
        
        # Window configuration
        if self.window_config:
            for key, value in self.window_config.items():
                config[f"window.{key}"] = value
        
        # WebGL configuration
        if self.webgl_config:
            config.update(self.webgl_config)
        
        # Audio configuration
        if self.audio_config:
            for key, value in self.audio_config.items():
                config[f"AudioContext:{key}"] = value
        
        # Geolocation
        if self.geolocation_config:
            for key, value in self.geolocation_config.items():
                config[f"geolocation:{key}"] = value
        
        # Timezone and locale
        if self.timezone:
            config["timezone"] = self.timezone
        
        if self.locale:
            parts = self.locale.split("-")
            if len(parts) >= 2:
                config["locale:language"] = parts[0]
                config["locale:region"] = parts[1]
        
        # Fonts
        if self.fonts_config and self.fonts_config.get("fonts"):
            config["fonts"] = self.fonts_config["fonts"]
        
        # Additional fingerprint config
        if self.fingerprint_config:
            config.update(self.fingerprint_config)
        
        return config
    
    def update_usage(self):
        """Update usage statistics"""
        self.usage_count += 1
        self.last_used = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_active": self.is_active,
            "proxy_id": self.proxy_id,
            "proxy_info": self.proxy_info,
            "user_agent": self.user_agent,
            "timezone": self.timezone,
            "locale": self.locale,
            "usage_count": self.usage_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
    
    def __repr__(self):
        return f"<BrowserProfile(id={self.id}, name='{self.name}', active={self.is_active})>"
