"""
Proxy Service for managing proxy configurations
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from loguru import logger

from models.proxy import Proxy, ProxyType, ProxyStatus
from camoufox_integration.proxy_manager import Proxy<PERSON>anager
from core.database import get_async_session


class ProxyService:
    """Service for managing proxies"""
    
    def __init__(self):
        self.proxy_manager = ProxyManager()
    
    async def create_proxy(
        self,
        name: str,
        proxy_type: ProxyType,
        host: str,
        port: int,
        username: Optional[str] = None,
        password: Optional[str] = None,
        description: Optional[str] = None,
        ssh_private_key: Optional[str] = None,
        ssh_passphrase: Optional[str] = None,
        validate_on_create: bool = True
    ) -> Proxy:
        """Create a new proxy"""
        
        async for session in get_async_session():
            try:
                # Check if proxy already exists
                existing = await session.execute(
                    select(Proxy).where(
                        Proxy.host == host,
                        Proxy.port == port,
                        Proxy.proxy_type == proxy_type
                    )
                )
                if existing.scalar_one_or_none():
                    raise ValueError(f"Proxy {host}:{port} ({proxy_type.value}) already exists")
                
                # Create proxy
                proxy = Proxy(
                    name=name,
                    description=description,
                    proxy_type=proxy_type,
                    host=host,
                    port=port,
                    username=username,
                    password=password,  # TODO: Encrypt password
                    ssh_private_key=ssh_private_key,  # TODO: Encrypt key
                    ssh_passphrase=ssh_passphrase,  # TODO: Encrypt passphrase
                    status=ProxyStatus.INACTIVE
                )
                
                session.add(proxy)
                await session.commit()
                await session.refresh(proxy)
                
                # Validate proxy if requested
                if validate_on_create:
                    await self.validate_proxy(proxy.id)
                
                logger.info(f"Created proxy: {proxy.name}")
                return proxy
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error creating proxy: {e}")
                raise
    
    async def get_proxy(self, proxy_id: int) -> Optional[Proxy]:
        """Get proxy by ID"""
        
        async for session in get_async_session():
            try:
                return await session.get(Proxy, proxy_id)
            except Exception as e:
                logger.error(f"Error getting proxy {proxy_id}: {e}")
                return None
    
    async def get_proxies(
        self,
        active_only: bool = False,
        proxy_type: Optional[ProxyType] = None,
        status: Optional[ProxyStatus] = None,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[Proxy]:
        """Get list of proxies"""
        
        async for session in get_async_session():
            try:
                query = select(Proxy)

                if active_only:
                    query = query.where(Proxy.is_active == True)

                if proxy_type:
                    query = query.where(Proxy.proxy_type == proxy_type)

                if status:
                    query = query.where(Proxy.status == status)

                query = query.offset(offset)
                if limit:
                    query = query.limit(limit)

                result = await session.execute(query)
                return result.scalars().all()

            except Exception as e:
                logger.error(f"Error getting proxies: {e}")
                return []
    
    async def update_proxy(
        self,
        proxy_id: int,
        **updates
    ) -> Optional[Proxy]:
        """Update proxy"""
        
        async for session in get_async_session():
            try:
                proxy = await session.get(Proxy, proxy_id)
                if not proxy:
                    return None
                
                # Update fields
                for key, value in updates.items():
                    if hasattr(proxy, key):
                        setattr(proxy, key, value)
                
                await session.commit()
                await session.refresh(proxy)
                
                logger.info(f"Updated proxy: {proxy.name}")
                return proxy
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating proxy {proxy_id}: {e}")
                raise
    
    async def delete_proxy(self, proxy_id: int) -> bool:
        """Delete proxy"""
        
        async for session in get_async_session():
            try:
                proxy = await session.get(Proxy, proxy_id)
                if not proxy:
                    return False
                
                # Check if proxy is in use
                from models.browser_profile import BrowserProfile
                profiles_using = await session.execute(
                    select(BrowserProfile).where(BrowserProfile.proxy_id == proxy_id)
                )
                if profiles_using.scalars().first():
                    raise ValueError("Cannot delete proxy that is assigned to browser profiles")
                
                await session.delete(proxy)
                await session.commit()
                
                logger.info(f"Deleted proxy: {proxy.name}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error deleting proxy {proxy_id}: {e}")
                raise
    
    async def validate_proxy(self, proxy_id: int) -> Dict[str, Any]:
        """Validate proxy connectivity"""
        
        async for session in get_async_session():
            try:
                proxy = await session.get(Proxy, proxy_id)
                if not proxy:
                    return {"success": False, "error": "Proxy not found"}
                
                # Update status to testing
                proxy.status = ProxyStatus.TESTING
                await session.commit()
                
                # Validate using proxy manager
                is_valid = await self.proxy_manager.validate_proxy(proxy)
                
                # Get geolocation if validation successful
                geo_info = None
                if is_valid:
                    geo_info = await self.proxy_manager.get_proxy_geolocation(proxy)
                
                # Update proxy in database
                session.add(proxy)
                await session.commit()
                
                result = {
                    "success": is_valid,
                    "proxy_id": proxy_id,
                    "response_time": proxy.response_time_ms,
                    "status": proxy.status.value,
                    "ip_address": proxy.ip_address,
                    "geolocation": geo_info,
                    "error": proxy.last_error
                }
                
                logger.info(f"Validated proxy {proxy.name}: {'Success' if is_valid else 'Failed'}")
                return result
                
            except Exception as e:
                logger.error(f"Error validating proxy {proxy_id}: {e}")
                return {"success": False, "error": str(e)}
    
    async def validate_all_proxies(self) -> Dict[str, Any]:
        """Validate all proxies"""
        
        try:
            proxies = await self.get_proxies(active_only=True)
            
            if not proxies:
                return {"success": True, "message": "No proxies to validate"}
            
            # Use proxy manager for batch validation
            results = await self.proxy_manager.health_check_all(proxies)
            
            # Update database with results
            async for session in get_async_session():
                for proxy in proxies:
                    session.add(proxy)
                await session.commit()
            
            logger.info(f"Validated {len(proxies)} proxies")
            return {"success": True, "results": results}
            
        except Exception as e:
            logger.error(f"Error validating all proxies: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_proxy_statistics(self) -> Dict[str, Any]:
        """Get proxy statistics"""
        
        async for session in get_async_session():
            try:
                proxies = await session.execute(select(Proxy))
                all_proxies = proxies.scalars().all()
                
                stats = {
                    "total": len(all_proxies),
                    "active": 0,
                    "inactive": 0,
                    "error": 0,
                    "testing": 0,
                    "by_type": {},
                    "avg_response_time": 0,
                    "success_rate": 0
                }
                
                total_response_time = 0
                total_requests = 0
                total_successful = 0
                
                for proxy in all_proxies:
                    # Count by status
                    if proxy.status == ProxyStatus.ACTIVE:
                        stats["active"] += 1
                    elif proxy.status == ProxyStatus.INACTIVE:
                        stats["inactive"] += 1
                    elif proxy.status == ProxyStatus.ERROR:
                        stats["error"] += 1
                    elif proxy.status == ProxyStatus.TESTING:
                        stats["testing"] += 1
                    
                    # Count by type
                    proxy_type = proxy.proxy_type.value
                    stats["by_type"][proxy_type] = stats["by_type"].get(proxy_type, 0) + 1
                    
                    # Calculate averages
                    if proxy.response_time_ms:
                        total_response_time += proxy.response_time_ms
                    
                    total_requests += proxy.total_requests
                    total_successful += (proxy.total_requests - proxy.failed_requests)
                
                # Calculate averages
                if stats["total"] > 0:
                    stats["avg_response_time"] = total_response_time / stats["total"]
                
                if total_requests > 0:
                    stats["success_rate"] = (total_successful / total_requests) * 100
                
                return stats
                
            except Exception as e:
                logger.error(f"Error getting proxy statistics: {e}")
                return {}
    
    async def rotate_proxy(self, current_proxy_id: Optional[int] = None) -> Optional[Proxy]:
        """Get next proxy in rotation"""
        
        try:
            # Get all active proxies
            active_proxies = await self.get_proxies(
                active_only=True,
                status=ProxyStatus.ACTIVE
            )
            
            if not active_proxies:
                return None
            
            # Use proxy manager for rotation
            next_proxy = await self.proxy_manager.rotate_proxy(active_proxies)
            
            if next_proxy:
                logger.info(f"Rotated to proxy: {next_proxy.name}")
            
            return next_proxy
            
        except Exception as e:
            logger.error(f"Error rotating proxy: {e}")
            return None
    
    async def test_proxy_with_url(
        self,
        proxy_id: int,
        test_url: str = "https://httpbin.org/ip"
    ) -> Dict[str, Any]:
        """Test proxy with specific URL"""
        
        try:
            proxy = await self.get_proxy(proxy_id)
            if not proxy:
                return {"success": False, "error": "Proxy not found"}
            
            # Create a test browser instance
            from camoufox_integration.browser_manager import BrowserManager
            browser_manager = BrowserManager()
            
            # Create minimal profile for testing
            from models.browser_profile import BrowserProfile
            test_profile = BrowserProfile(
                name="test_profile",
                fingerprint_config={}
            )
            
            # Launch browser with proxy
            browser = await browser_manager.create_browser_instance(
                profile=test_profile,
                proxy=proxy,
                headless=True
            )
            
            context = await browser_manager.create_browser_context(browser, test_profile, proxy)
            page = await context.new_page()
            
            # Navigate to test URL
            response = await page.goto(test_url, timeout=30000)
            content = await page.content()
            
            # Cleanup
            await page.close()
            await browser_manager.close_context(context)
            await browser_manager.close_browser(browser)
            
            return {
                "success": True,
                "status_code": response.status,
                "url": test_url,
                "content_length": len(content),
                "proxy_used": proxy.name
            }
            
        except Exception as e:
            logger.error(f"Error testing proxy {proxy_id} with URL {test_url}: {e}")
            return {"success": False, "error": str(e)}
    
    async def import_proxies_from_list(
        self,
        proxy_list: List[Dict[str, Any]],
        validate_on_import: bool = False
    ) -> Dict[str, Any]:
        """Import multiple proxies from list"""
        
        results = {
            "total": len(proxy_list),
            "imported": 0,
            "skipped": 0,
            "errors": 0,
            "details": []
        }
        
        for proxy_data in proxy_list:
            try:
                # Validate required fields
                required_fields = ["name", "proxy_type", "host", "port"]
                if not all(field in proxy_data for field in required_fields):
                    results["errors"] += 1
                    results["details"].append({
                        "proxy": proxy_data.get("name", "Unknown"),
                        "status": "error",
                        "message": "Missing required fields"
                    })
                    continue
                
                # Create proxy
                proxy = await self.create_proxy(
                    name=proxy_data["name"],
                    proxy_type=ProxyType(proxy_data["proxy_type"]),
                    host=proxy_data["host"],
                    port=proxy_data["port"],
                    username=proxy_data.get("username"),
                    password=proxy_data.get("password"),
                    description=proxy_data.get("description"),
                    validate_on_create=validate_on_import
                )
                
                results["imported"] += 1
                results["details"].append({
                    "proxy": proxy.name,
                    "status": "imported",
                    "id": proxy.id
                })
                
            except ValueError as e:
                # Proxy already exists
                results["skipped"] += 1
                results["details"].append({
                    "proxy": proxy_data.get("name", "Unknown"),
                    "status": "skipped",
                    "message": str(e)
                })
                
            except Exception as e:
                results["errors"] += 1
                results["details"].append({
                    "proxy": proxy_data.get("name", "Unknown"),
                    "status": "error",
                    "message": str(e)
                })
        
        logger.info(f"Imported {results['imported']} proxies, skipped {results['skipped']}, errors {results['errors']}")
        return results

    async def test_proxy_with_url(self, proxy_id: int, test_url: str) -> Dict[str, Any]:
        """Test proxy with specific URL"""

        async for session in get_async_session():
            try:
                proxy = await session.get(Proxy, proxy_id)
                if not proxy:
                    return {"success": False, "error": "Proxy not found"}

                # Update status to testing
                proxy.status = ProxyStatus.TESTING
                await session.commit()

                # Test proxy with custom URL
                start_time = time.time()

                try:
                    import aiohttp

                    # Build proxy URL
                    proxy_url = proxy.proxy_url

                    # Test based on proxy type
                    if proxy.proxy_type in [ProxyType.HTTP, ProxyType.HTTPS]:
                        async with aiohttp.ClientSession(
                            timeout=aiohttp.ClientTimeout(total=30)
                        ) as client_session:
                            async with client_session.get(test_url, proxy=proxy_url) as response:
                                response_time = int((time.time() - start_time) * 1000)

                                result = {
                                    "success": True,
                                    "proxy_id": proxy_id,
                                    "test_url": test_url,
                                    "status_code": response.status,
                                    "response_time": response_time,
                                    "content_length": len(await response.text()),
                                    "headers": dict(response.headers)
                                }

                                # Update proxy stats
                                proxy.update_stats(True, response_time)
                                proxy.update_health_check(True)

                                session.add(proxy)
                                await session.commit()

                                return result

                    else:
                        # For SOCKS proxies, use requests with socks support
                        import requests

                        proxies = {
                            'http': proxy_url,
                            'https': proxy_url
                        }

                        response = requests.get(
                            test_url,
                            proxies=proxies,
                            timeout=30
                        )

                        response_time = int((time.time() - start_time) * 1000)

                        result = {
                            "success": True,
                            "proxy_id": proxy_id,
                            "test_url": test_url,
                            "status_code": response.status_code,
                            "response_time": response_time,
                            "content_length": len(response.text),
                            "headers": dict(response.headers)
                        }

                        # Update proxy stats
                        proxy.update_stats(True, response_time)
                        proxy.update_health_check(True)

                        session.add(proxy)
                        await session.commit()

                        return result

                except Exception as e:
                    error_msg = str(e)
                    response_time = int((time.time() - start_time) * 1000)

                    # Update proxy with error
                    proxy.update_stats(False, response_time)
                    proxy.update_health_check(False, error_msg)

                    session.add(proxy)
                    await session.commit()

                    return {
                        "success": False,
                        "proxy_id": proxy_id,
                        "test_url": test_url,
                        "error": error_msg,
                        "response_time": response_time
                    }

            except Exception as e:
                logger.error(f"Error testing proxy {proxy_id} with URL {test_url}: {e}")
                return {"success": False, "error": str(e)}
