/**
 * EditProfileForm Component - Form for editing existing profiles
 */

import React, { useState, useEffect } from 'react';
import { FiX, FiGlobe } from 'react-icons/fi';
import { profileService } from '../../services/profileService';

const EditProfileForm = ({ isOpen, onClose, onSubmit, profile }) => {
  const [formData, setFormData] = useState({
    proxyType: 'no-proxy',
    ipChecker: 'IP2Location',
    host: '',
    port: '',
    username: '',
    password: '',
    profileName: ''
  });
  const [proxyStatus, setProxyStatus] = useState(null);
  const [isChecking, setIsChecking] = useState(false);

  const proxyTypes = [
    { value: 'no-proxy', label: 'No proxy (local network)' },
    { value: 'http', label: 'HTTP' },
    { value: 'https', label: 'HTTPS' },
    { value: 'socks5', label: 'SOCKS5' },
    { value: 'ssh', label: 'SSH' }
  ];

  const ipCheckers = [
    { value: 'IP2Location', label: 'IP2Location' },
    { value: 'ip-api', label: 'ip-api' },
    { value: 'IPIDEA', label: 'IPIDEA' },
    { value: 'IPFoxy', label: 'IPFoxy' },
    { value: 'IPInfo', label: 'IPInfo' }
  ];

  // Load profile data when profile changes
  useEffect(() => {
    if (profile) {
      // Parse proxy info from profile.proxy string (e.g., "*************:8080")
      const proxyParts = profile.proxy ? profile.proxy.split(':') : [];
      
      setFormData({
        profileName: profile.username || '',
        proxyType: profile.proxyType || 'no-proxy',
        ipChecker: 'IP2Location',
        host: proxyParts[0] || '',
        port: proxyParts[1] || '',
        username: profile.proxyUsername || '',
        password: profile.proxyPassword || ''
      });
      setProxyStatus(null);
    }
  }, [profile]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckProxy = async () => {
    if (formData.proxyType === 'no-proxy') {
      setProxyStatus({
        success: true,
        message: 'Local network connection',
        ip: 'Local IP',
        location: 'Local Network'
      });
      return;
    }

    if (!formData.host || !formData.port) {
      alert('Vui lòng nhập Host và Port');
      return;
    }

    setIsChecking(true);
    setProxyStatus(null);

    try {
      // Use profile service to validate proxy
      const result = await profileService.validateProxy(formData);

      if (result.success) {
        setProxyStatus({
          success: true,
          message: 'Proxy connection successful',
          ip: result.ip_address || 'Unknown',
          location: result.geolocation ?
            `${result.geolocation.city}, ${result.geolocation.country}` :
            'Unknown Location',
          responseTime: result.response_time
        });
      } else {
        setProxyStatus({
          success: false,
          message: 'Proxy connection failed',
          error: result.error || 'Unknown error'
        });
      }
    } catch (error) {
      setProxyStatus({
        success: false,
        message: 'Proxy validation failed',
        error: error.message
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.profileName) {
      alert('Vui lòng nhập tên profile');
      return;
    }

    if (formData.proxyType !== 'no-proxy' && (!formData.host || !formData.port)) {
      alert('Vui lòng nhập thông tin proxy');
      return;
    }

    onSubmit(profile.id, formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      proxyType: 'no-proxy',
      ipChecker: 'IP2Location',
      host: '',
      port: '',
      username: '',
      password: '',
      profileName: ''
    });
    setProxyStatus(null);
    onClose();
  };

  if (!isOpen || !profile) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Sửa Profile</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tên Profile
            </label>
            <input
              type="text"
              name="profileName"
              value={formData.profileName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nhập tên profile"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Proxy Type
            </label>
            <select
              name="proxyType"
              value={formData.proxyType}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {proxyTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              IP Checker
            </label>
            <select
              name="ipChecker"
              value={formData.ipChecker}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {ipCheckers.map(checker => (
                <option key={checker.value} value={checker.value}>
                  {checker.label}
                </option>
              ))}
            </select>
          </div>

          {formData.proxyType !== 'no-proxy' && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Host
                  </label>
                  <input
                    type="text"
                    name="host"
                    value={formData.host}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="***********"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Port
                  </label>
                  <input
                    type="number"
                    name="port"
                    value={formData.port}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="8080"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tùy chọn"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Tùy chọn"
                  />
                </div>
              </div>

              <div>
                <button
                  type="button"
                  onClick={handleCheckProxy}
                  disabled={isChecking}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isChecking ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Đang kiểm tra...</span>
                    </>
                  ) : (
                    <>
                      <FiGlobe className="w-4 h-4" />
                      <span>Kiểm tra Proxy</span>
                    </>
                  )}
                </button>
              </div>

              {proxyStatus && (
                <div className={`p-3 rounded-md ${proxyStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <div className={`text-sm font-medium ${proxyStatus.success ? 'text-green-800' : 'text-red-800'}`}>
                    {proxyStatus.message}
                  </div>
                  {proxyStatus.success && (
                    <div className="text-xs text-green-600 mt-1 space-y-1">
                      <div>IP: {proxyStatus.ip}</div>
                      <div>Location: {proxyStatus.location}</div>
                      {proxyStatus.responseTime && (
                        <div>Response Time: {proxyStatus.responseTime}ms</div>
                      )}
                    </div>
                  )}
                  {!proxyStatus.success && proxyStatus.error && (
                    <div className="text-xs text-red-600 mt-1">
                      Error: {proxyStatus.error}
                    </div>
                  )}
                </div>
              )}
            </>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Cập nhật
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditProfileForm;
