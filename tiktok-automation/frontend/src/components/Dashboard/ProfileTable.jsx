/**
 * ProfileTable Component - Displays profile management table
 */

import React, { useState } from 'react';
import { FiPlay, FiPause, FiStopCircle, FiShield } from 'react-icons/fi';
import AntidetectTester from './AntidetectTester';

const ProfileTable = ({
  profiles,
  selectedProfiles,
  onProfileSelect,
  onProfileAction,
  onSelectAll
}) => {
  const [showAntidetectTester, setShowAntidetectTester] = useState(false);
  const [selectedProfileForTest, setSelectedProfileForTest] = useState(null);
  const getStatusColor = (status) => {
    switch (status) {
      case 'SẴN SÀNG':
        return 'bg-green-100 text-green-800';
      case 'ĐANG NHẬP HOẠT ĐỘ':
        return 'bg-blue-100 text-blue-800';
      case 'CHƯA ĐĂNG NHẬP':
        return 'bg-yellow-100 text-yellow-800';
      case 'LỖI':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderActionButtons = (profile) => {
    const buttons = [];
    
    if (profile.actions.includes('login')) {
      buttons.push(
        <button
          key="login"
          onClick={() => onProfileAction(profile.id, 'login')}
          className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
        >
          Đăng nhập
        </button>
      );
    }
    
    if (profile.actions.includes('complete')) {
      buttons.push(
        <button
          key="complete"
          onClick={() => onProfileAction(profile.id, 'complete')}
          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors"
        >
          Hoàn tất đăng nhập
        </button>
      );
    }
    
    if (profile.actions.includes('start')) {
      buttons.push(
        <button
          key="start"
          onClick={() => onProfileAction(profile.id, 'start')}
          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors flex items-center space-x-1"
        >
          <FiPlay className="w-3 h-3" />
          <span>Bắt đầu</span>
        </button>
      );
    }
    
    if (profile.actions.includes('pause')) {
      buttons.push(
        <button
          key="pause"
          onClick={() => onProfileAction(profile.id, 'pause')}
          className="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700 transition-colors flex items-center space-x-1"
        >
          <FiPause className="w-3 h-3" />
          <span>Tạm dừng</span>
        </button>
      );
    }
    
    if (profile.actions.includes('stop')) {
      buttons.push(
        <button
          key="stop"
          onClick={() => onProfileAction(profile.id, 'stop')}
          className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors flex items-center space-x-1"
        >
          <FiStopCircle className="w-3 h-3" />
          <span>Dừng</span>
        </button>
      );
    }

    // Always add antidetect test button
    buttons.push(
      <button
        key="antidetect"
        onClick={() => {
          setSelectedProfileForTest(profile.id);
          setShowAntidetectTester(true);
        }}
        className="bg-purple-600 text-white px-3 py-1 rounded text-xs hover:bg-purple-700 transition-colors flex items-center space-x-1"
        title="Test Antidetect Capabilities"
      >
        <FiShield className="w-3 h-3" />
        <span>Test</span>
      </button>
    );

    return (
      <div className="flex space-x-2 flex-wrap">
        {buttons}
      </div>
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input 
                type="checkbox" 
                className="rounded border-gray-300"
                onChange={(e) => onSelectAll(e.target.checked)}
                checked={selectedProfiles.length === profiles.length && profiles.length > 0}
              />
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Proxy</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Follow hôm nay</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Follow phiên này</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hoạt động (cuối)</th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hành động</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {profiles.map((profile) => (
            <tr key={profile.id} className="hover:bg-gray-50">
              <td className="px-4 py-4 whitespace-nowrap">
                <input 
                  type="checkbox" 
                  className="rounded border-gray-300"
                  checked={selectedProfiles.includes(profile.id)}
                  onChange={(e) => onProfileSelect(profile.id, e.target.checked)}
                />
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.stt}</td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.username}</td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.proxy}</td>
              <td className="px-4 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(profile.status)}`}>
                  {profile.status}
                </span>
              </td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.followersToday}</td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.followersFollowed}</td>
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{profile.currentAction}</td>
              <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                {renderActionButtons(profile)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Antidetect Tester Modal */}
      {showAntidetectTester && selectedProfileForTest && (
        <AntidetectTester
          profileId={selectedProfileForTest}
          onClose={() => {
            setShowAntidetectTester(false);
            setSelectedProfileForTest(null);
          }}
        />
      )}
    </div>
  );
};

export default ProfileTable;
